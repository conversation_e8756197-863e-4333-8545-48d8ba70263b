#!/bin/bash

# CodeRocket MCP Pre-commit Hook
# 在提交前自动进行代码审查

echo "🔍 正在进行提交前代码审查..."

# 检查是否有暂存的文件
if git diff --cached --quiet; then
    echo "⚠️ 没有暂存的文件，跳过代码审查"
    exit 0
fi

# 检查 coderocket-mcp 是否可用
if ! command -v npx &> /dev/null; then
    echo "❌ npx 不可用，跳过代码审查"
    exit 0
fi

# 尝试进行代码审查
echo "📝 正在审查最新提交..."

# 使用 coderocket-mcp 进行代码审查
if npx @yeepay/coderocket-mcp review_changes --no-unstaged; then
    echo "✅ 代码审查通过"
    exit 0
else
    echo "❌ 代码审查失败"
    echo "💡 请修复问题后重新提交，或使用 --no-verify 跳过检查"

    # 检查是否强制允许提交
    if [[ "${CODEROCKET_ALLOW_COMMIT:-false}" == "true" ]]; then
        echo "⚠️ 强制允许提交（CODEROCKET_ALLOW_COMMIT=true）"
        exit 0
    fi

    exit 1
fi
